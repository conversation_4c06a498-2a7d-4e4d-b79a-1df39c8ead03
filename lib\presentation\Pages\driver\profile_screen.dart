import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/controller/profile_controller.dart';

import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/theme/app_styles.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/core/responsive/responsive_text.dart';
import 'package:driver_app/core/responsive/responsive_layout.dart';
import 'package:driver_app/core/responsive/responsive_padding.dart';
import 'package:driver_app/presentation/widgets/dialogs/logout_confirmation_dialog.dart';

import 'package:driver_app/presentation/widgets/profile/profile_avatar_widget.dart';
import 'package:driver_app/presentation/widgets/common/circular_app_icon.dart';
// import '../../../core/routes/app_routes.dart';
// import '../../widgets/navigation/navigation_button_bar.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final UserController _userController = Get.find<UserController>();
  // int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // Initialize profile controller
    Get.put(ProfileController());
  }

  @override
  void dispose() {
    // Clean up controller when widget is disposed
    Get.delete<ProfileController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: ResponsiveLayout(
        mobile: _buildContent(context),
        tablet: _buildContent(context),
        desktop: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),

      body: SingleChildScrollView(
        child: ResponsivePadding.all(
          context,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              _buildProfileHeader(context),
              Divider(color: AppColors.dividerColor),
              _buildProfileOptions(context),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFF2196F3),
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      title: const Text(
        'الملف الشخصي',
        style: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontFamily: 'Cairo',
        ),
      ),
      centerTitle: true,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_forward,
          color: AppColors.white,
          size: ResponsiveHelper.getResponsiveIconSize(context),
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        // Notifications button
        IconButton(
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
          onPressed: () {
            // Handle notifications
          },
        ),
        // أيقونة التطبيق الدائرية
        // Padding(
        //   padding: const EdgeInsets.symmetric(horizontal: 8.0),
        //   child: CircularAppIconButton(
        //     radius: 18,
        //     showBorder: true,
        //     borderColor: Colors.white,
        //     borderWidth: 1.5,
        //     showShadow: false,
        //     onTap: () {
        //       // يمكن إضافة عمل مخصص هنا
        //     },
        //   ),
        // ),
      ],
    );
  }

  Widget _buildProfileHeader(BuildContext context) {
    return Container(
      padding: ResponsiveHelper.getResponsiveMargin(context),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.05),
            AppColors.primary.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.getResponsiveBorderRadius(context),
        ),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.1)),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.05),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),

      child: Column(
        children: [
          // Profile Image Section
          ProfileAvatarWidget(
            radius: ResponsiveHelper.getValue(context, 55.0, 65.0, 75.0),
            defaultImagePath: 'assets/images/logo2.png',
            showEditButton: true,
          ),
          AnimatedContainer(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
            padding: EdgeInsets.symmetric(
              horizontal: ResponsiveHelper.getResponsiveSpacing(context),
              vertical: ResponsiveHelper.getResponsiveSpacing(context) * 0.5,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Column(children: [_buildUserInfo(context)]),
          ),

          SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),

          // Tasks Overview Section
          //_buildTasksOverview(context, isArabic),
        ],
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    final user = _userController.user.value; // احصل على بيانات المستخدم

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // الاسم
        ResponsiveText(
          user?.fullName ?? '---', // استخدم الاسم من الـ Controller
          baseFontSize: 24,
          style: AppStyles.getHeadingStyle(context).copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primaryText,
            letterSpacing: 0.5,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.3),
        // البريد الإلكتروني
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.email_outlined,
              size: 16,
              color: AppColors.primary.withValues(alpha: 0.7),
            ),
            SizedBox(width: 8),
            ResponsiveText(
              user?.email ?? '---', // استخدم الإيميل من الـ Controller
              baseFontSize: 16,
              style: AppStyles.getBodyStyle(context).copyWith(
                color: AppColors.secondaryText,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
        Container(
          width: 100,
          height: 2,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.primary.withValues(alpha: 0.1),
                AppColors.primary.withValues(alpha: 0.5),
                AppColors.primary.withValues(alpha: 0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(1),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileOptions(BuildContext context) {
    return Padding(
      padding: ResponsiveHelper.getResponsiveMargin(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.2),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            margin: EdgeInsets.symmetric(vertical: 4),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              child: Column(
                children: [
                  Theme(
                    data: Theme.of(context).copyWith(
                      splashColor: AppColors.primary.withValues(alpha: 0.1),
                      highlightColor: AppColors.primary.withValues(alpha: 0.05),
                    ),
                  //   child: _buildOptionTile(
                  //     context,
                  //     icon: Icons.settings,
                  //     title: 'الإعدادات',
                  //     onTap: () => Get.toNamed(AppRoutes.settings),
                  //   ),
                  // ),
                  // Divider(
                  //   height: 1,
                  //   color: AppColors.primary.withValues(alpha: 0.1),
                  // ),
                  // Theme(
                  //   data: Theme.of(context).copyWith(
                  //     splashColor: AppColors.primary.withValues(alpha: 0.1),
                  //     highlightColor: AppColors.primary.withValues(alpha: 0.05),
                  //   ),
                    child: _buildOptionTile(
                      context,
                      icon: Icons.notifications,
                      title: 'الإشعارات',
                      onTap: () => Get.toNamed('/notifications'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // SizedBox(height: 10),
          // Container(
          //   decoration: BoxDecoration(
          //     borderRadius: BorderRadius.circular(12),
          //     border: Border.all(
          //       color: AppColors.primary.withValues(alpha: 0.2),
          //     ),
          //     color: Colors.white,
          //     boxShadow: [
          //       BoxShadow(
          //         color: AppColors.primary.withValues(alpha: 0.1),
          //         blurRadius: 10,
          //         offset: const Offset(0, 2),
          //       ),
          //     ],
          //   ),
          //   margin: EdgeInsets.symmetric(vertical: 4),
          //   child: Material(
          //     color: Colors.transparent,
          //     borderRadius: BorderRadius.circular(12),
          //     child: Column(children: [
               
          //       ],
          //     ),
          //   ),
          // ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
              color: AppColors.error.withValues(alpha: 0.05),
            ),
            margin: EdgeInsets.symmetric(vertical: 4),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              child: Theme(
                data: Theme.of(context).copyWith(
                  splashColor: AppColors.error.withValues(alpha: 0.1),
                  highlightColor: AppColors.error.withValues(alpha: 0.05),
                ),
                child: _buildOptionTile(
                  context,
                  icon: Icons.logout,
                  title: 'تسجيل الخروج',
                  textColor: AppColors.error,
                  iconColor: AppColors.error,
                  onTap: () => _showLogoutDialog(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
    Color? iconColor,
  }) {
    return ListTile(
      leading: null,
      trailing: Icon(
        icon,
        color: iconColor ?? AppColors.primary,
        size: ResponsiveHelper.getResponsiveIconSize(context),
      ),
      title: ResponsiveText(
        title,
        baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
        style: AppStyles.getBodyStyle(
          context,
        ).copyWith(color: textColor ?? AppColors.primaryText),
        textAlign: TextAlign.right,
      ),
      onTap: onTap,
    );
  }

  void _showLogoutDialog() {
    Get.dialog(const LogoutConfirmationDialog());
  }
}
